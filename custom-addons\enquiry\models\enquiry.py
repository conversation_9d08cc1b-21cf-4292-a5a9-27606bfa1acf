from odoo import api, fields, models, _
from odoo.exceptions import UserError
from datetime import datetime
import time
import json
import base64
import logging

_logger = logging.getLogger(__name__)

class Enquiry(models.Model):
    _name = 'enquiry.enquiry'
    _description = 'Enquiry'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    # Basic identification fields
    name = fields.Char('Enquiry ID', required=True, readonly=True, default=lambda self: _('New'))
    create_date = fields.Datetime('Creation Date', readonly=True)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    # Client basic information (Form 1)
    customer_name = fields.Char('Customer Name', required=True, tracking=True)
    email = fields.Char('Email', required=True, tracking=True)
    phone = fields.Char('Phone Number', required=True, tracking=True)
    company_name = fields.Char('Company Name', required=True, tracking=True)
    designation = fields.Char('Designation', tracking=True)
    event_id = fields.Many2one('event.event', string="Related Event", readonly=True, copy=False)

    # Tax related fields
    gst_number = fields.Char('GST Number')
    pan_number = fields.Char('PAN Number')
    gst_registered = fields.Boolean('GST Registered', compute='_compute_gst_registered', store=True)
    
    @api.depends('gst_number')
    def _compute_gst_registered(self):
        """Automatically determine if GST registered based on GST number"""
        for record in self:
            record.gst_registered = bool(record.gst_number and record.gst_number.strip())

    # Enquiry classification (Form 1)
    enquiry_type = fields.Selection([
        ('design_engineering', 'Design & Engineering (D&E)'),
        ('fabrication_prototyping', 'Fabrication/Prototyping (Fab/Proto)'),
        ('startup_support', 'Startup Support'),
        ('partnerships', 'Partnerships'),
        ('events_workshops', 'Events/Workshops'),
        ('vendor_onboarding', 'Vendor Onboarding'),
        ('visits', 'Visits'),
        ('others', 'Others')
    ], string='Type of Enquiry', required=True, tracking=True)

    # Design & Engineering fields
    design_brief = fields.Text('Design Brief', tracking=True, 
                               help="Brief description of design requirements for D&E enquiries")

    # Fabrication/Prototyping capabilities required
    fabrication_capabilities = fields.Many2many('enquiry.fabrication.capability', 
                                               string='Required Capabilities',
                                               tracking=True)

    # For easier handling in the view
    fabrication_capabilities_ids = fields.Many2many('enquiry.fabrication.capability', 
                                                   string='Capability IDs',
                                                   compute='_compute_capability_ids',
                                                   store=False)

    # Capability-specific attachment fields
    metal_shop_attachment_ids = fields.Many2many('ir.attachment',
                                                 string='Metal Shop Attachments',
                                                 relation='enquiry_metal_shop_attachment_rel',
                                                 tracking=True)

    printing_3d_attachment_ids = fields.Many2many('ir.attachment',
                                                  string='3D Printing Attachments',
                                                  relation='enquiry_3d_printing_attachment_rel',
                                                  tracking=True)

    laser_cutting_attachment_ids = fields.Many2many('ir.attachment',
                                                    string='Laser Cutting Attachments',
                                                    relation='enquiry_laser_cutting_attachment_rel',
                                                    tracking=True)

    wood_shop_attachment_ids = fields.Many2many('ir.attachment',
                                                string='Wood Shop Attachments',
                                                relation='enquiry_wood_shop_attachment_rel',
                                                tracking=True)

    ceramic_studio_attachment_ids = fields.Many2many('ir.attachment',
                                                    string='Ceramic Studio Attachments',
                                                    relation='enquiry_ceramic_studio_attachment_rel',
                                                    tracking=True)

    arp_attachment_ids = fields.Many2many('ir.attachment',
                                                string='Advanced Rapid Prototyping Attachments',
                                                relation='enquiry_arp_attachment_rel',
                                                tracking=True)

    electronics_attachment_ids = fields.Many2many('ir.attachment',
                                                string='Electronics Attachments',
                                                relation='enquiry_electronics_attachment_rel',
                                                tracking=True)

    textile_attachment_ids = fields.Many2many('ir.attachment',
                                                string='Textile Attachments',
                                                relation='enquiry_textile_attachment_rel',
                                                tracking=True)

    # For backward compatibility with older versions
    capability_attachments_ids = fields.Many2many('ir.attachment', 
                                                string='Capability Attachments',
                                                relation='enquiry_capability_attachment_rel',
                                                tracking=True,
                                                help="Attachments for specific fabrication capabilities",
                                                compute="_compute_capability_attachments",
                                                store=False)
    
    fabrication_capabilities_display = fields.Char('Capabilities', compute='_compute_capabilities_display')

    verticals = fields.Selection([
        ('agriculture', 'Agriculture'),
        ('aquaculture', 'Aquaculture'),
        ('animal_husbandry', 'Animal Husbandry'),
        ('assistive_technology', 'Assistive Technology'),
        ('automotive', 'Automotive & Auto Components'),
        ('aviation', 'Aviation'),
        ('aerospace', 'Aerospace'),
        ('bfsi', 'BFSI'),
        ('biotechnology', 'Biotechnology'),
        ('chemicals', 'Chemicals'),
        ('construction_infrastructure', 'Construction & Infrastructure'),
        ('defence', 'Defence'),
        ('drones_unmanned_vehicles', 'Drones & Unmanned Vehicles'),
        ('education', 'Education'),
        ('electronics_wearables', 'Electronics & Wearables'),
        ('energy_power', 'Energy and Power'),
        ('food_processing_dairy', 'Food Processing & Dairy'),
        ('furniture_consumer_products', 'Furniture & Consumer Products'),
        ('handicrafts_traditional_crafts', 'Handicrafts and Traditional Crafts'),
        ('maritime', 'Maritime'),
        ('healthcare_medical_devices', 'Healthcare and Medical Devices'),
        ('leather_footwear', 'Leather & Footwear'),
        ('manufacturing_special_purpose_machines', 'Manufacturing & Special Purpose Machines'),
        ('oil_gas_mining', 'Oil & Gas / Mining'),
        ('paper_packaging', 'Paper & Packaging'),
        ('social_innovation', 'Social Innovation'),
        ('sr_faculty_hod', 'Sr Faculty & HOD (SFDP)'),
        ('textiles', 'Textiles'),
        ('tourism', 'Tourism'),
        ('others', 'Others')
    ], string='Vertical', required=True, tracking=True)
    verticals_other = fields.Char('Other Vertical', tracking=True)

    visitor_type = fields.Selection([
        ('corporate', 'Corporate'),
        ('individual', 'Individual'),
        ('startup', 'Startup'),
        ('student', 'Student'),
        ('vendor', 'Vendor'),
        ('other', 'Other')
    ], string='Type of Visitor', required=True, tracking=True)
    visitor_type_other = fields.Char('Other Visitor Type', tracking=True)

    project_name = fields.Char('Project Name', required=True, tracking=True)
    nda_required = fields.Boolean('NDA Required', tracking=True)

    # Detailed client information (Form 2)
    completed_registration = fields.Boolean('Completed Registration', default=False, tracking=True, 
                                           help="Indicates if all client information is filled")

    # Information section - consolidated fields
    billing_street = fields.Char('Billing Street')
    billing_city = fields.Char('Billing City')
    billing_zip = fields.Char('Billing ZIP')
    billing_country_id = fields.Many2one('res.country', string='Billing Country')

    shipping_street = fields.Char('Shipping Street')
    shipping_city = fields.Char('Shipping City')
    shipping_zip = fields.Char('Shipping ZIP')
    shipping_country_id = fields.Many2one('res.country', string='Shipping Country')

    # Shipping same as billing field
    shipping_same_as_billing = fields.Boolean('Shipping same as Billing', default=False, 
                                            help="When checked, shipping address will be automatically updated with billing address")

    specific_requirements = fields.Text('Specific Requirements')

    # Attachments for Form 2
    attachment_ids = fields.Many2many('ir.attachment', 
                                     string='Attachments',
                                     relation='enquiry_information_attachment_rel')

    sow_attachment_ids = fields.Many2many(
        'ir.attachment',
        string='Scope of Work',
        relation='enquiry_sow_attachment_rel',
        help="Attach Scope of Work documents"
    )
    deliverables_attachment_ids = fields.Many2many('ir.attachment', 
                                                string='Work Report',
                                                relation='enquiry_deliverables_attachment_rel')

    # Project notes field
    project_notes = fields.Text('Project Notes', help="Notes, updates, and important information for the project")

    referred_by = fields.Selection([
        ('t_hub', 'T-hub'),
        ('employee', 'Employee'),
        ('walk_ins', 'Walk-ins'),
        ('website', 'Website'),
        ('existing_customer', 'Existing Customer'),
        ('visits', 'Visits'),
        ('events_workshops', 'Events/Workshops'),
        ('community_groups', 'Community Groups'),
        ('social_media', 'Social Media'),
        ('other', 'Other')
    ], string='Referred By')
    referred_by_other = fields.Char('Other Referral Source')
    
    # Requested deadline date field
    requested_deadline_date = fields.Date('Requested Deadline Date', tracking=True, help="Expected completion date requested by the client")

    # Project deadline field
    project_deadline = fields.Date('Project Deadline', tracking=True, help="Deadline for project completion")

    # Field to store the customer form update link
    information_update_link = fields.Char('Information Update Link', readonly=True)

    # Project management fields
    stage_id = fields.Many2one('enquiry.stage', string='Stage', 
                              default=lambda self: self.env['enquiry.stage'].search([('sequence', '=', 1)], limit=1),
                              group_expand='_read_group_stage_ids', tracking=True)

    priority = fields.Selection([
        ('0', 'Engage Slowly'),
        ('1', 'Evaluate Critically'),
        ('2', 'Please Proceed'),
        ('3', 'Max Priority')
    ], string='Priority', default='1', tracking=True)

    stage_time_start = fields.Datetime('Current Stage Start Time', readonly=True)
    stage_duration = fields.Float('Current Stage Duration (Days)', compute='_compute_stage_duration', store=False)
    stage_duration_human = fields.Char('Time in Stage', compute='_compute_stage_duration', store=False)
    stage_history_ids = fields.One2many('enquiry.stage.history', 'enquiry_id', string='Stage History')

    client_info_finalized = fields.Boolean('Client Information Finalized', default=False, help="When true, client information can only be edited through the wizard")

    last_presales_updated = fields.Datetime(string="Last Pre-Sales Update Time", default=fields.Datetime.now) # DIWAKER :- Time stamp for last pre-sales updated in projects to enquiry and vice versa

        # --- Event/Workshop Fields (visible only when enquiry_type is 'events_workshops') ---

    # Event Details
    event_name = fields.Char("Event Name")
    event_type = fields.Selection([
        ('workshop', 'Workshop'),
        ('expo', 'Expo'),
        ('exhibition', 'Exhibition'),
        ('competition', 'Competition'),
        ('demo_day', 'Demo Day'),
        ('panel', 'Panel Discussion'),
        ('hackathon', 'Hackathon'),
        ('meetup', 'Meetup'),
        ('conference', 'Conference'),
    ], string="Event Type")
    event_theme = fields.Char("Event Theme")
    event_description = fields.Text("Event Description")
    estimated_attendees = fields.Integer("Estimated Number of Attendees")

    # Venue & Time Preferences
    event_venue = fields.Selection([
        ('to_be_decided', 'To Be Decided'),
        ('event_space', 'Event Space'),
        ('library', 'Library'),
        ('events_room', 'Events Room'),
        ('atrium', 'Atrium'),
    ], string="Venue")
    event_time_slot = fields.Selection([
        ('morning', 'Morning'),
        ('afternoon', 'Afternoon'),
        ('evening', 'Evening'),
    ], string="Time Slot")

    # Dates
    event_date_begin = fields.Datetime("Event Start Date")
    event_date_end = fields.Datetime("Event End Date")

    # Additional Requirements (match events model exactly)
    requirement_line_ids = fields.One2many('enquiry.requirement.line', 'enquiry_id', string="Additional Requirements")
    addl_requirements_other = fields.Text("Other Requirements", help="Specify any additional requirements not listed above")

    # Vendor, Revenue & Communication Details (match events model exactly)
    event_sponsored = fields.Boolean("Is the Event Sponsored?")
    revenue_sharing = fields.Boolean("Is Revenue Sharing Involved?")
    expected_revenue_type = fields.Selection([
        ('ticket_sales', 'Ticket Sales'),
        ('sponsorship', 'Sponsorship'),
        ('registration_fees', 'Registration Fees'),
        ('vendor_fees', 'Vendor Fees'),
        ('other', 'Other'),
    ], string="Expected Revenue Type")
    expected_revenue_type_other = fields.Char("Other Revenue Type")
    revenue_sharing_agreed = fields.Boolean("Revenue Sharing Agreed with T-Works?")
    expected_revenue_range = fields.Selection([
        ('0-10k', '₹0 - ₹10,000'),
        ('10k-50k', '₹10,000 - ₹50,000'),
        ('50k-1l', '₹50,000 - ₹1,00,000'),
        ('1l-5l', '₹1,00,000 - ₹5,00,000'),
        ('5l+', '₹5,00,000+'),
    ], string="Expected Revenue Range")
    proposed_revenue_split_tworks = fields.Float("T-Works Share (%)", default=0.0)
    proposed_revenue_split_vendor = fields.Float("Vendor Share (%)", default=0.0)
    ticketing_collection_by_tworks = fields.Boolean("T-Works to manage ticketing & collection?")
    preferred_communication = fields.Selection([
        ('email', 'Email'),
        ('phone', 'Phone'),
        ('whatsapp', 'WhatsApp'),
        ('in_person', 'In Person'),
        ('video_call', 'Video Call'),
    ], string="Preferred Mode of Future Communication")

    @api.depends('stage_time_start')
    def _compute_stage_duration(self):
        """Compute the duration in the current stage"""
        for record in self:
            if record.stage_time_start:
                # Calculate duration between stage start and now
                delta = fields.Datetime.now() - record.stage_time_start
                # Convert to days
                days = delta.total_seconds() / (24 * 60 * 60)
                record.stage_duration = days
                
                # Human readable format
                if days < 1:
                    hours = delta.total_seconds() / 3600
                    if hours < 1:
                        minutes = delta.total_seconds() / 60
                        record.stage_duration_human = f"{int(minutes)} min"
                    else:
                        record.stage_duration_human = f"{int(hours)} hours"
                elif days < 30:
                    record.stage_duration_human = f"{int(days)} days"
                else:
                    months = days / 30
                    record.stage_duration_human = f"{int(months)} months"
            else:
                record.stage_duration = 0
                record.stage_duration_human = "N/A"

    @api.onchange('stage_id')
    def _onchange_stage_id(self):
        """Handle stage change to record timing"""
        result = super(Enquiry, self)._onchange_stage_id()
        # This only handles UI changes, actual history is recorded in write method
        self.stage_time_start = fields.Datetime.now()
        return result

    def _compute_capabilities_display(self):
        """Compute a display string for fabrication capabilities"""
        for record in self:
            if record.fabrication_capabilities:
                # Get first 2 capabilities and count the rest
                capabilities = record.fabrication_capabilities.mapped('name')
                if len(capabilities) <= 2:
                    record.fabrication_capabilities_display = ', '.join(capabilities)
                else:
                    record.fabrication_capabilities_display = f"{capabilities[0]}, {capabilities[1]} +{len(capabilities)-2}"
            else:
                record.fabrication_capabilities_display = False

    # Responsible persons
    submitted_by_id = fields.Many2one('hr.employee', string='Submitted By', tracking=True)
    company_poc_id = fields.Many2one('hr.employee', string='T-Works PoC', tracking=True)
    bd_poc_id = fields.Many2one('hr.employee', string='BD Point of Contact', tracking=True, domain="[('department_id.name', 'ilike', 'Business Development')]")

    # Follow-up management
    followup_date = fields.Date('Follow-up Due Date', tracking=True)

    # HSN Fields
    design_hsn = fields.Char('HSN/SAC', default='998130', readonly=True)
    prototyping_hsn = fields.Char('HSN/SAC', default='998130', readonly=True)

    # Pre-sales quote information with quantity
    design_time = fields.Float('Design Time (Hours)', tracking=True)
    design_quantity = fields.Integer('Design Quantity', default=1, tracking=True)
    design_cost = fields.Float('Design Cost', tracking=True)
    design_total = fields.Float('Design Total', compute='_compute_subtotals', store=True)

    material_cost = fields.Float('Material Cost', tracking=True)

    prototyping_quantity = fields.Integer('Prototyping Quantity', default=1, tracking=True)
    prototyping_cost = fields.Float('Prototyping Cost', tracking=True)
    prototyping_total = fields.Float('Prototyping Total', compute='_compute_subtotals', store=True)

    #Events/Workshops
    venue_cost = fields.Float("Venue Cost")
    additional_costs = fields.Float("Additional Costs", default=0.0)
    gst_percentage = fields.Float("GST (%)", default=18.0)
    gst = fields.Float("GST Amount", compute='_compute_gst', store=True)

    total_cost = fields.Float('Total Cost', compute='_compute_total_cost', store=True, tracking=True)

    # Custom line items
    custom_line_item_ids = fields.One2many('enquiry.custom.line.item', 'enquiry_id', string='Custom Line Items')

    quotation_approved = fields.Boolean('Quotation Approved', tracking=True)
    quotation_approved_by_id = fields.Many2one('hr.employee', string='Approved By', tracking=True)
    quotation_approval_date = fields.Datetime('Approval Date', tracking=True)

    # Payment fields
    payment_link = fields.Char('Payment Link', help="Link to online payment portal")
    quote_attachment_ids = fields.Many2many('ir.attachment',
                                        string='Quote Documents',
                                        relation='enquiry_quote_attachment_rel')
    payment_status = fields.Selection([
        ('pending', 'Pending'),
        ('paid', 'Paid'),
        ('failed', 'Failed'),
        ('po_pending', 'PO Pending Approval'),
        ('po_approved', 'PO Approved'),
        ('bank_pending', 'Bank Transfer Pending Verification'),
        ('bank_verified', 'Bank Transfer Verified')
    ], string='Payment Status', default='pending')

    # Purchase Order fields
    po_attachment_ids = fields.Many2many('ir.attachment', 
                                    string='Purchase Order Documents',
                                    relation='enquiry_po_attachment_rel')
    
    transaction_reference = fields.Char(string="Transaction Reference")

    # Bank Transfer fields
    utr_id = fields.Char('UTR/Reference ID')
    utr_screenshot_ids = fields.Many2many('ir.attachment', 
                                        string='Payment Screenshots',
                                        relation='enquiry_utr_attachment_rel')
    
    can_close_enquiry = fields.Boolean(
        'Can Close Enquiry',
        compute='_compute_can_close_enquiry',
        help="Technical field to check if current user can close this enquiry"
    )

    @api.depends('stage_id', 'payment_status')
    def _compute_can_close_enquiry(self):
        """Compute if the current user can close this enquiry"""
        for record in self:
            # Allow managers to close at any stage
            if self.env.user.has_group('enquiry.group_enquiry_manager'):
                record.can_close_enquiry = True
                continue
                
            # Business Development users can close at any stage before payment/active
            if self.env.user.has_group('enquiry.group_enquiry_business_development'):
                if record.payment_status != 'paid' and record.stage_id.name != 'Active':
                    record.can_close_enquiry = True
                    continue
            
            # Only Operations Admin and Accounts can close paid enquiries
            if record.payment_status == 'paid' or record.stage_id.name == 'Active':
                record.can_close_enquiry = (self.env.user.has_group('enquiry.group_enquiry_operations_admin') or 
                                        self.env.user.has_group('enquiry.group_enquiry_accounts'))
                continue
            
            # For unpaid enquiries, allow regular users to close
            if self.env.user.has_group('enquiry.group_enquiry_user'):
                record.can_close_enquiry = True
            else:
                record.can_close_enquiry = False

    # Closure information
    close_reason = fields.Selection([
        ('high_quotation', 'Cost Exceeds Client Budget'),
        ('non_compliant_files', 'Non-Compliant Design Files'),
        ('quotation_only', 'Quotation Request Only'),
        ('manufacturing_limitations', 'Manufacturing Not Feasible'),
        ('cost_complexity_mismatch', 'Complex Design with Minimal Budget'),
        ('project_cancellation', 'Project No Longer Required'),
        ('alternative_sourcing', 'Client Sourced from Market'),
        ('duplicate_enquiry', 'Duplicate Enquiry'),
        ('startup_pass_holder', 'Startup Pass Holder - Category 1 Tools'),
        ('no_response', 'No Response After Multiple Follow-ups'),
        ('delayed_response', 'Client Lost Due to Delayed Response'),
        ('internal_approval_denied', 'Internal Order Approval Denied'),
        ('other', 'Other Reason (See Notes)')
    ], string='Close Reason', tracking=True)

    close_reason_notes = fields.Text('Additional Notes', tracking=True,
                                help="Additional details about closure when 'Other' is selected")
    closed_date = fields.Datetime('Closed Date', tracking=True)

    # Add relation to proforma invoices
    proforma_invoice_ids = fields.One2many('enquiry.proforma.invoice', 'enquiry_id', string='Pro Forma Invoices')
    proforma_invoice_count = fields.Integer('Pro Forma Invoices', compute='_compute_proforma_invoice_count')
    
    # API integration fields
    api_token = fields.Char('API Token', readonly=True, copy=False)
    api_token_expiry = fields.Datetime('API Token Expiry', readonly=True, copy=False)
    api_enabled = fields.Boolean('API Enabled', compute='_compute_api_enabled', store=False)

    awaiting_approval = fields.Boolean(string="Awaiting Approval", default=False)

    def action_display_awaiting_message(self):
        """Display a message when quotation is awaiting approval"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Awaiting Approval'),
                'message': _('This quotation has been sent for approval and is awaiting manager review.'),
                'type': 'warning',
                'sticky': False,
            }
        }

    @api.depends_context('uid')
    def _compute_api_enabled(self):
        """Compute whether API is enabled in system settings"""
        param_value = self.env['ir.config_parameter'].sudo().get_param('enquiry.api_enabled', 'False')
        api_enabled = param_value.lower() in ('true', '1', 't', 'y', 'yes')
        
        for record in self:
            record.api_enabled = api_enabled
            
        # For debugging
        _logger.info(f"API Enabled setting: {api_enabled} (param value: {param_value})")

    @api.model
    def create(self, vals):
        if vals.get('name', _('New')) == _('New'):
            current_year = datetime.now().strftime('%y')
            next_year = str(int(current_year) + 1)
            fiscal_year = current_year + '-' + next_year
            vals['name'] = 'TW/' + fiscal_year + '/ENQ/' + self.env['ir.sequence'].next_by_code('enquiry.sequence') or 'New'

        enquiry = super(Enquiry, self).create(vals)

        # Auto-create event if enquiry type is events/workshops
        # TEMPORARILY DISABLED FOR TESTING
        # if (enquiry.enquiry_type == 'events_workshops' and
        #     hasattr(enquiry, 'customer_name') and enquiry.customer_name):
        #     try:
        #         enquiry._create_or_update_event()
        #     except Exception as e:
        #         _logger.warning(f"Could not auto-create event for new enquiry {enquiry.id}: {e}")

        return enquiry

    @api.depends('design_cost', 'design_quantity', 'material_cost', 'prototyping_cost', 'prototyping_quantity', 'custom_line_item_ids.cost', 'custom_line_item_ids.quantity')
    def _compute_subtotals(self):
        for record in self:
            record.design_total = record.design_cost * record.design_quantity
            record.prototyping_total = record.prototyping_cost * record.prototyping_quantity

    @api.depends('design_total', 'material_cost', 'prototyping_total', 'custom_line_item_ids.cost', 'custom_line_item_ids.quantity','venue_cost', 'additional_costs', 'gst')
    def _compute_total_cost(self):
        for record in self:
            if record.enquiry_type == 'events_workshops':
            # Only for events/workshops
                record.total_cost = (record.venue_cost or 0.0) + (record.additional_costs or 0.0) + (record.gst or 0.0)
            else:
            # Standard general cost logic
                standard_costs = (record.design_total or 0.0) + (record.material_cost or 0.0) + (record.prototyping_total or 0.0)
                custom_costs = sum((item.cost or 0.0) * (item.quantity or 0) for item in record.custom_line_item_ids)
                record.total_cost = standard_costs + custom_costs

    @api.depends('venue_cost', 'additional_costs', 'gst_percentage')
    def _compute_gst(self):
        for rec in self:
            base = (rec.venue_cost or 0.0) + (rec.additional_costs or 0.0)
            rec.gst = base * (rec.gst_percentage / 100.0) if base else 0.0
    @api.depends('proforma_invoice_ids')
    def _compute_proforma_invoice_count(self):
        for record in self:
            record.proforma_invoice_count = len(record.proforma_invoice_ids)

    @api.depends('fabrication_capabilities')
    def _compute_capability_ids(self):
        """Convert fabrication capabilities to a list of IDs for use in the view"""
        for record in self:
            if record.fabrication_capabilities:
                record.fabrication_capabilities_ids = record.fabrication_capabilities.ids
            else:
                record.fabrication_capabilities_ids = []

    @api.depends('metal_shop_attachment_ids', 'printing_3d_attachment_ids', 'laser_cutting_attachment_ids', 
                'wood_shop_attachment_ids', 'ceramic_studio_attachment_ids', 'arp_attachment_ids', 
                'electronics_attachment_ids', 'textile_attachment_ids')
    def _compute_capability_attachments(self):
        """Combine all capability-specific attachments into a single field for backward compatibility"""
        for record in self:
            all_attachments = []
            if record.metal_shop_attachment_ids:
                all_attachments.extend(record.metal_shop_attachment_ids.ids)
            if record.printing_3d_attachment_ids:
                all_attachments.extend(record.printing_3d_attachment_ids.ids)
            if record.laser_cutting_attachment_ids:
                all_attachments.extend(record.laser_cutting_attachment_ids.ids)
            if record.wood_shop_attachment_ids:
                all_attachments.extend(record.wood_shop_attachment_ids.ids)
            if record.ceramic_studio_attachment_ids:
                all_attachments.extend(record.ceramic_studio_attachment_ids.ids)
            if record.arp_attachment_ids:
                all_attachments.extend(record.arp_attachment_ids.ids)
            if record.electronics_attachment_ids:
                all_attachments.extend(record.electronics_attachment_ids.ids)
            if record.textile_attachment_ids:
                all_attachments.extend(record.textile_attachment_ids.ids)

            record.capability_attachments_ids = all_attachments

    @api.onchange('fabrication_capabilities')
    def _onchange_fabrication_capabilities(self):
        """Handle UI updates when fabrication capabilities change"""
        # This method is primarily for UI feedback, actual logic handled by the view
        if self.fabrication_capabilities:
            # Process for UI feedback if needed
            pass

    @api.model
    def _read_group_stage_ids(self, stages, domain, order):
        """Read all stages for group by stage feature"""
        stage_ids = self.env['enquiry.stage'].search([])
        return stage_ids

    @api.onchange('shipping_same_as_billing', 'billing_street', 'billing_city', 'billing_zip', 'billing_country_id')
    def _onchange_shipping_same_as_billing(self):
        """Copy billing address to shipping address when the checkbox is checked"""
        if self.shipping_same_as_billing:
            self.update({
                'shipping_street': self.billing_street,
                'shipping_city': self.billing_city,
                'shipping_zip': self.billing_zip,
                'shipping_country_id': self.billing_country_id,
            })

    @api.onchange('billing_street', 'billing_city', 'billing_zip', 'billing_country_id',
                 'shipping_street', 'shipping_city', 'shipping_zip', 'shipping_country_id',
                 'customer_name', 'email', 'phone', 'company_name')
    
    def _onchange_client_info(self):
        """Check if all client information is filled to set completed_registration"""
        required_fields = [
            self.customer_name, self.email, self.phone, self.company_name,
            self.billing_street, self.billing_city, self.billing_zip, self.billing_country_id,
            self.shipping_street, self.shipping_city, self.shipping_zip, self.shipping_country_id
        ]

        self.completed_registration = all(required_fields)

    def action_show_registration_required(self):
        """Show message that registration must be completed"""
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Registration Required'),
                'message': _('Please complete the client registration before proceeding to requirements.'),
                'type': 'warning',
                'sticky': False,
            }
        }

    def action_show_awaiting_approval(self):
        """Display notification when awaiting approval button is clicked"""
        self.ensure_one()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Awaiting Approval'),
                'message': _('This quotation is currently awaiting approval from an Operations Administrator manager.'),
                'type': 'warning',
                'sticky': False,
            }
        }

    @api.onchange('stage_id')
    def _onchange_stage_id(self):
        """Handle actions when stage changes"""
        # If moving to Awaiting Requirements stage, ensure BD PoC is set
        awaiting_requirements = self.env['enquiry.stage'].search([('name', '=', 'Awaiting Requirements')], limit=1)
        if awaiting_requirements and self.stage_id.id == awaiting_requirements.id and not self.bd_poc_id:
            return {
                'warning': {
                    'title': _('Missing BD Point of Contact'),
                    'message': _('Please set a BD Point of Contact before moving to the Awaiting Requirements stage.')
                }
            }

    @api.onchange('verticals')
    def _onchange_verticals(self):
        """Handle display of other verticals field"""
        if self.verticals != 'others':
            self.verticals_other = False

    @api.onchange('visitor_type')
    def _onchange_visitor_type(self):
        """Handle display of other visitor type field"""
        if self.visitor_type != 'other':
            self.visitor_type_other = False

    @api.onchange('referred_by')
    def _onchange_referred_by(self):
        """Handle display of other referral field"""
        if self.referred_by != 'other':
            self.referred_by_other = False

    @api.onchange('completed_registration')
    def _onchange_registration_completed(self):
        """When registration is completed, make sure client record is updated"""
        if self.completed_registration and self.customer_name and self.email and self.phone:
            # This is for UI feedback only since onchange doesn't persist
            return {
                'warning': {
                    'title': _('Client Record'),
                    'message': _('Client record will be created/updated on save')
                }
            }

    def write(self, vals):
        """Override write to update client records when relevant fields change and track stage changes"""
        # Track stage change - do this before super call to capture old stage correctly
        if 'stage_id' in vals:
            # Get the old stage 
            for record in self:
                old_stage_id = record.stage_id.id
                old_stage_name = record.stage_id.name
                
                # Calculate time spent in current stage
                if record.stage_time_start:
                    time_spent = fields.Datetime.now() - record.stage_time_start
                    time_spent_hours = time_spent.total_seconds() / 3600
                    
                    # Record stage history
                    self.env['enquiry.stage.history'].create({
                        'enquiry_id': record.id,
                        'stage_id': old_stage_id,
                        'stage_name': old_stage_name,
                        'date_start': record.stage_time_start,
                        'date_end': fields.Datetime.now(),
                        'time_spent_hours': time_spent_hours,
                    })
                
                # Update stage time
                vals['stage_time_start'] = fields.Datetime.now()

        self._update_presales_timestamp(vals)

        # --- ACTUAL WRITE ---
        result = super(Enquiry, self).write(vals)
        
        if 'company_poc_id' in vals:
            for enquiry in self:
                project = self.env['project.project'].search([('enquiry_id', '=', enquiry.id)], limit=1)
                if project:
                    project.project_poc = vals['company_poc_id']

        # --- UPDATE LINKED CLIENT RECORD IF NEEDED ---
        self._update_client_record(vals)

        # --- SYNC PRESALES DATA FROM PROJECT (ONLY IF NOT LOOPING) ---
        if not self.env.context.get('syncing_presales'):
            self.sync_presales_from_project()
            self.sync_presales_to_project()  # Optional: only if syncing back to project is desired
            self._sync_project_stage_and_create(vals)
        return result

    def _create_update_client_record(self):
        """Internal method to create or update a client record"""
        self.ensure_one()

        # Skip if essential fields are missing
        if not (self.customer_name and self.email and self.phone):
            return False

        # Check if client already exists based on email
        existing_client = self.env['crm.client'].search([('email', '=', self.email)], limit=1)

        # Prepare billing address
        billing_address = ""
        if self.billing_street:
            billing_address = self.billing_street
            if self.billing_city or self.billing_zip:
                billing_address += "\n"
                if self.billing_city:
                    billing_address += self.billing_city
                if self.billing_zip:
                    billing_address += ", " + self.billing_zip

        # Prepare shipping address
        shipping_address = ""
        if not self.shipping_same_as_billing and self.shipping_street:
            shipping_address = self.shipping_street
            if self.shipping_city or self.shipping_zip:
                shipping_address += "\n"
                if self.shipping_city:
                    shipping_address += self.shipping_city
                if self.shipping_zip:
                    shipping_address += ", " + self.shipping_zip
        elif self.shipping_same_as_billing:
            shipping_address = billing_address

        # Prepare values for client record
        client_vals = {
            'name': self.customer_name,
            'email': self.email,
            'phone': self.phone,
            'company_name': self.company_name,
            'billing_address': billing_address,
            'shipping_address': shipping_address,
            'completed_registration': self.completed_registration,
            'nda_required': self.nda_required,
            'bd_poc': self.bd_poc_id.id if self.bd_poc_id else False,
        }

        # Handle client type if it exists in the client model
        if hasattr(self, 'visitor_type') and hasattr(self.env['crm.client'], 'client_type'):
            if self.visitor_type:
                client_vals['client_type'] = self.visitor_type

        # Handle industry if it exists in the client model
        if hasattr(self, 'verticals') and hasattr(self.env['crm.client'], 'industry'):
            if self.verticals:
                client_vals['industry'] = self.verticals

        # Create or update the client record
        if existing_client:
            existing_client.write(client_vals)
            client_record = existing_client
        else:
            client_record = self.env['crm.client'].create(client_vals)

        # Optionally update statistics fields if they exist
        if hasattr(client_record, 'enquiry_count'):
            client_record.enquiry_count = self.env['enquiry.enquiry'].search_count([('email', '=', self.email)])

        if hasattr(client_record, 'last_enquiry_id'):
            client_record.last_enquiry_id = self.id

        return client_record
    
    def action_open_client_selection(self):
        """Open the client selection wizard"""
        self.ensure_one()
        return {
            'name': _('Select Existing Client'),
            'type': 'ir.actions.act_window',
            'res_model': 'enquiry.select.client.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'active_id': self.id}
        }
    
    def action_edit_client_information(self):
        """Open the client edit wizard"""
        self.ensure_one()
        return {
            'name': _('Edit Client Information'),
            'type': 'ir.actions.act_window',
            'res_model': 'enquiry.edit.client.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'active_id': self.id}
        }
    
    def action_finalize_client_info(self):
        """Finalize client information and make it readonly"""
        self.ensure_one()
        
        # Check if required fields are filled
        required_fields = [
            self.customer_name, self.email, self.phone, self.company_name
        ]
        
        if not all(required_fields):
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Missing Information'),
                    'message': _('Please fill in all required client information before finalizing.'),
                    'type': 'warning',
                }
            }
        
        # Set client info as finalized
        self.write({'client_info_finalized': True})
        
        # Also update the CRM client record if it exists or create one
        client = self.env['crm.client'].search([('email', '=', self.email)], limit=1)
        
        if client:
            # Update existing client
            client.write({
                'name': self.customer_name,
                'email': self.email,
                'phone': self.phone,
                'company_name': self.company_name,
            })
        else:
            # Create new client
            self._create_update_client_record()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Client Information Finalized'),
                'message': _('Client information has been finalized. Use the Edit button to make changes.'),
                'type': 'success',
            }
        }

    def action_generate_update_link(self):
        """Generate a unique link for the client to update their information"""
        self.ensure_one()

        # Check if basic info is filled
        if not self.customer_name or not self.email or not self.phone:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Missing Information'),
                    'message': _('Please fill in at least customer name, email, and phone number before generating a link.'),
                    'type': 'warning',
                    'sticky': False,
                }
            }

        # Generate a new token
        token_record = self.env['enquiry.access.token'].sudo().generate_token(self.id, token_type='update_form')

        # Create the full URL
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        update_url = f"{base_url}/enquiry/update/{token_record.token}"

        # Store the URL
        self.write({
            'information_update_link': update_url
        })

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Link Generated'),
                'message': _('Information update link has been generated and can be shared with the client.'),
                'type': 'success',
                'sticky': False,
            }
        }

    def action_send_update_link(self):
        """Send the information update link to the client"""
        # First ensure we have a link generated
        if not self.information_update_link:
            self.action_generate_update_link()

        # This would integrate with email templates to send the link
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Link Sent'),
                'message': _('Information update link has been sent to the client.'),
                'type': 'success',
                'sticky': False,
            }
        }

    def action_send_for_approval(self):
        """Send quote for approval to Operations Administrators"""
        # Approval logic to be implemented here
        self.write({'awaiting_approval': True})
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Approval Request'),
                'message': _('Quote has been sent for approval.'),
                'type': 'success',
            }
        }

    def action_approve_quotation(self):
        """Approve the quotation and move to next stage"""
        self.ensure_one()
        quotation_sent_stage = self.env['enquiry.stage'].search([('name', '=', 'Quotation Sent')], limit=1)
        if quotation_sent_stage:
            self.write({
                'quotation_approved': True,
                'quotation_approved_by_id': self.env.user.employee_id.id,
                'quotation_approval_date': fields.Datetime.now(),
                'stage_id': quotation_sent_stage.id
            })
        else:
            self.write({
                'quotation_approved': True,
                'quotation_approved_by_id': self.env.user.employee_id.id,
                'quotation_approval_date': fields.Datetime.now(),
            })
        return True

    def action_close_enquiry(self):
        """Close the enquiry with a reason"""
        return {
            'name': _('Close Enquiry'),
            'type': 'ir.actions.act_window',
            'res_model': 'enquiry.close.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_enquiry_id': self.id}
        }

    # Pre-sales actions
    def action_get_quote(self):
        """View or Generate a pro forma invoice for the enquiry"""
        self.ensure_one()
        return self.action_generate_proforma_invoice()

    def action_view_proforma_invoices(self):
        """View all pro forma invoices for this enquiry"""
        self.ensure_one()
        return {
            'name': _('Pro Forma Invoices'),
            'view_mode': 'tree,form',
            'res_model': 'enquiry.proforma.invoice',
            'domain': [('enquiry_id', '=', self.id)],
            'type': 'ir.actions.act_window',
            'context': {'default_enquiry_id': self.id}
        }

    def action_generate_proforma_invoice(self):
        """Generate or view existing pro forma invoice from this enquiry"""
        self.ensure_one()

        # Check if there's an existing invoice
        existing_invoice = self.env['enquiry.proforma.invoice'].search([
            ('enquiry_id', '=', self.id),
            ('state', 'not in', ['cancelled'])  # Exclude cancelled invoices
        ], limit=1, order='create_date DESC')
        
        create_new = False
        
        if existing_invoice:
            # Check if costs have changed since last invoice
            if existing_invoice.state == 'draft':
                # For draft invoices, check if costs have changed
                current_total = self._calculate_expected_invoice_total()
                if abs(existing_invoice.total - current_total) > 0.01:  # Small tolerance for floating point comparison
                    create_new = True
                    
                # If costs differ, ask user if they want to update existing or create new
                if create_new:
                    return {
                        'name': _('Invoice Values Changed'),
                        'type': 'ir.actions.act_window',
                        'res_model': 'enquiry.invoice.update.wizard',
                        'view_mode': 'form',
                        'target': 'new',
                        'context': {
                            'default_enquiry_id': self.id,
                            'default_existing_invoice_id': existing_invoice.id,
                            'default_current_total': current_total,
                            'default_invoice_total': existing_invoice.total,
                        }
                    }
            
            # If costs haven't changed or invoice is not draft, just view the existing invoice
            if not create_new:
                return {
                    'name': _('Pro Forma Invoice'),
                    'view_mode': 'form',
                    'res_model': 'enquiry.proforma.invoice',
                    'res_id': existing_invoice.id,
                    'type': 'ir.actions.act_window',
                }
        
        # Create new invoice
        invoice_lines = self._prepare_invoice_lines()
        
        # Create the invoice
        invoice = self.env['enquiry.proforma.invoice'].create({
            'enquiry_id': self.id,
            'line_ids': invoice_lines,
        })

        self._attach_proforma_invoice_pdf(invoice)

        # Open the invoice in form view
        return {
            'name': _('Pro Forma Invoice'),
            'view_mode': 'form',
            'res_model': 'enquiry.proforma.invoice',
            'res_id': invoice.id,
            'type': 'ir.actions.act_window',
        }

    def _prepare_invoice_lines(self):
        """Prepare line items for invoice creation"""
        invoice_lines = []

        # Add design cost if present
        if self.design_cost and self.design_quantity:
            invoice_lines.append((0, 0, {
                'name': 'Design Services',
                'quantity': self.design_quantity,
                'price_unit': self.design_cost,
                'hsn_code': self.design_hsn,
            }))

        # Add material cost if present
        if self.material_cost:
            invoice_lines.append((0, 0, {
                'name': 'Materials',
                'quantity': 1.0,
                'price_unit': self.material_cost,
                'hsn_code': '998130',
            }))

        # Add prototyping cost if present
        if self.prototyping_cost and self.prototyping_quantity:
            invoice_lines.append((0, 0, {
                'name': 'Prototyping Services',
                'quantity': self.prototyping_quantity,
                'price_unit': self.prototyping_cost,
                'hsn_code': self.prototyping_hsn,
            }))

        # Add custom line items if present
        for item in self.custom_line_item_ids:
            invoice_lines.append((0, 0, {
                'name': item.name,
                'quantity': item.quantity,
                'price_unit': item.cost,
                'hsn_code': item.hsn_code,
            }))
            
        return invoice_lines

    def _calculate_expected_invoice_total(self):
        """Calculate what the total would be for a new invoice based on current costs"""
        total = 0.0
        
        # Design costs
        if self.design_cost and self.design_quantity:
            total += self.design_cost * self.design_quantity
            
        # Material costs
        if self.material_cost:
            total += self.material_cost
            
        # Prototyping costs
        if self.prototyping_cost and self.prototyping_quantity:
            total += self.prototyping_cost * self.prototyping_quantity
            
        # Custom line items
        for item in self.custom_line_item_ids:
            total += item.cost
            
        # Add taxes (same 18% calculation as in ProformaInvoice._compute_amounts)
        total_with_tax = total * 1.18
        
        return total_with_tax

    def action_force_new_invoice(self):
        """Force creation of a new invoice regardless of existing ones"""
        self.ensure_one()
        
        # Prepare invoice lines
        invoice_lines = self._prepare_invoice_lines()
        
        # Create the invoice
        invoice = self.env['enquiry.proforma.invoice'].create({
            'enquiry_id': self.id,
            'line_ids': invoice_lines,
        })

        self._attach_proforma_invoice_pdf(invoice)

        # Open the invoice in form view
        return {
            'name': _('Pro Forma Invoice'),
            'view_mode': 'form',
            'res_model': 'enquiry.proforma.invoice',
            'res_id': invoice.id,
            'type': 'ir.actions.act_window',
        }
    
    def action_update_invoice_attachments(self):
        """"Manually update the pro forma invoice attachments"""
        self.ensure_one()

        if not self.proforma_invoice_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Invoices'),
                    'message': _('No pro forma invoices found to update attachments.'),
                    'type': 'warning',
                }
            }
        
        # Attach all invoices to quote documents
        for invoice in self.proforma_invoice_ids:
            self._attach_proforma_invoice_pdf(invoice)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Attachments Updated'),
                'message': _('Pro forma invoice attachments have been attached to Quote Documents.'),
                'type': 'success',
            }
        }

    def _attach_proforma_invoice_pdf(self, invoice):
        """Generate PDF for the pro forma invoice and attach it to quote documents"""
        self.ensure_one()

        try:
            # Direct approach - use the controller URL to get the PDF
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            report_url = f"{base_url}/report/pdf/enquiry.report_proforma_invoice/{invoice.id}"

            # Use a server action to download the PDF
            action = {
                'type': 'ir.actions.act_url',
                'url': report_url,
                'target': 'self',
            }

            # Create a simple text attachment as a fallback
            attachment_vals = {
                'name': f"Pro Forma Invoice - {invoice.invoice_number}.pdf",
                'type': 'url',
                'url': report_url,
                'res_model': self._name,
                'res_id': self.id,
            }
            attachment = self.env['ir.attachment'].create(attachment_vals)

            # Link the attachment to quote_attachment_ids
            self.write({
                'quote_attachment_ids': [(4, attachment.id)]
            })

            return attachment, action

        except Exception as e:
            _logger.error("Error generating PDF for pro forma invoice: %s", str(e))
            return None, {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Error generating PDF: %s') % str(e),
                    'type': 'warning',
                }
            }

    def action_update_proforma_attachments(self):
        """Update all pro forma invoice attachments for this enquiry"""
        self.ensure_one()

        # Check if there are invoices
        if not self.proforma_invoice_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Invoices'),
                    'message': _('No pro forma invoices found to update attachments.'),
                    'type': 'warning',
                }
            }

        # Remove old pro forma invoice attachments
        old_attachments = self.env['ir.attachment'].search([
            ('res_model', '=', self._name),
            ('res_id', '=', self.id),
            ('name', 'ilike', 'Pro Forma Invoice%'),
        ])
        if old_attachments:
            self.write({
                'quote_attachment_ids': [(3, att.id) for att in old_attachments]
            })

        # Create new attachments for each invoice
        for invoice in self.proforma_invoice_ids:
            self._attach_proforma_invoice_pdf(invoice)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Attachments Updated'),
                'message': _('Pro forma invoice attachments have been updated.'),
                'type': 'success',
            }
        }

    def action_refresh_payment_link(self):
        """Refresh payment link"""
        self.ensure_one()
        # Generate a new payment link
        payment_link = f"https://payment.example.com/pay/{self.id}?ts={int(time.time())}"
        self.write({'payment_link': payment_link})

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Payment Link'),
                'message': _('Payment link has been refreshed.'),
                'type': 'success',
            }
        }

    def action_print_attach_proforma(self):
        """Print the Pro forma invoice and attach it to quote documents."""
        self.ensure_one()

        # Check if there are any pro forma invoices
        if not self.proforma_invoice_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Invoices'),
                    'message': _('No pro forma invoices found. Please generate a quote first.'),
                    'type': 'warning',
                }
            }

        # Get the most recent invoice
        invoice = self.proforma_invoice_ids[0]

        # Try to attach the PDF
        result = self._attach_proforma_invoice_pdf(invoice)

        # If there was an error, return it
        if isinstance(result, dict) and result.get('type') == 'ir.actions.client':
            return result

        # If successful, return action to display the PDF
        if result and hasattr(result[0], 'id'):
            return {
                'type': 'ir.actions.act_url',
                'url': f'/web/content/{result[0].id}?download=true',
                'target': 'self',
            }

        # Fallback - just show invoice form
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'enquiry.proforma.invoice',
            'res_id': invoice.id,
            'view_mode': 'form',
            'target': 'current',
        }
        
    # Payment actions
    def action_confirm_payment(self):
        """Confirm online payment is completed"""
        self.payment_status = 'paid'
        # Move to Active stage
        active_stage = self.env['enquiry.stage'].search([('name', '=', 'Active')], limit=1)
        if active_stage:
            self.stage_id = active_stage.id
        return True

    def action_submit_po(self):
        """Submit purchase order for approval"""
        if not self.po_attachment_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Missing Information'),
                    'message': _('Please attach the purchase order document.'),
                    'type': 'warning',
                }
            }

        self.payment_status = 'po_pending'
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Purchase Order Submitted'),
                'message': _('Purchase order has been submitted for approval.'),
                'type': 'success',
            }
        }

    def action_submit_utr(self):
        """Submit bank transfer details"""
        if not self.utr_id or not self.utr_screenshot_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Missing Information'),
                    'message': _('Please provide the UTR/Reference ID and attach payment screenshot.'),
                    'type': 'warning',
                }
            }

        self.payment_status = 'bank_pending'
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Bank Transfer Details Submitted'),
                'message': _('Bank transfer details have been submitted for verification.'),
                'type': 'success',
            }
        }

    # New method to move from Onboarding to Awaiting Requirements
    def action_move_to_awaiting_requirements(self):
        """Move the enquiry from Onboarding to Awaiting Requirements stage"""
        self.ensure_one()

        # Check if required fields are filled
        required_fields = [
            self.customer_name, self.email, self.phone, self.company_name,
            self.project_name, self.enquiry_type
        ]

        if not all(required_fields):
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Missing Information'),
                    'message': _('Please fill in all required fields before proceeding.'),
                    'type': 'warning',
                    'sticky': False,
                }
            }

        # Find Awaiting Requirements stage
        awaiting_requirements = self.env['enquiry.stage'].search([('name', '=', 'Awaiting Requirements')], limit=1)

        if awaiting_requirements:
            self.write({
                'stage_id': awaiting_requirements.id
            })
            self.message_post(
                body=_('Enquiry has been moved to Awaiting Requirements stage.'),
                message_type='notification'
            )

            # Return an action to reload the form view
            return {
                'type': 'ir.actions.act_window',
                'res_model': 'enquiry.enquiry',
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'current',
                'context': {'stage_changed': True, 'new_stage': awaiting_requirements.name}
            }
            # # Uncomment the following lines if you want to show a notification instead of moving to the new stage  
            #   {
            #     'type': 'ir.actions.client',
            #     'tag': 'display_notification',
            #     'params': {
            #         'title': _('Stage Changed'),
            #         'message': _('Enquiry has been moved to Awaiting Requirements stage.'),
            #         'type': 'success',
            #         'sticky': False,
            #     }
            #   }

        return True
    
    can_move_to_presales = fields.Boolean(
        string='Can Move to Pre-Sales',
        compute='_compute_can_move_to_presales',
        store=False,
    )
    
    can_send_for_approval = fields.Boolean(
        string='Can Send for Approval',
        compute='_compute_can_send_for_approval',
        store=False,
    )

    is_in_presales_stage = fields.Boolean(
        string='Is in Pre-Sales Stage',
        compute='_compute_is_in_presales_stage',
        store=False,
        help="True when enquiry is in Pre-Sales stage"
    )
    
    @api.depends('company_poc_id', 'enquiry_type', 'design_brief', 'requested_deadline_date', 'fabrication_capabilities', 'attachment_ids')
    def _compute_can_move_to_presales(self):
        """Compute if the enquiry can be moved to Pre-Sales stage"""
        for record in self:
            # Start with checking common requirements
            # For events/workshops, only company_poc_id is required (no PRD needed)
            if record.enquiry_type == 'events_workshops':
                can_proceed = bool(record.company_poc_id)
            else:
                # For other types, both company_poc_id and attachment_ids (PRD) are required
                can_proceed = bool(record.company_poc_id and record.attachment_ids)

            # Add type-specific requirements
            if can_proceed and record.enquiry_type == 'design_engineering':
                can_proceed = bool(record.design_brief and record.requested_deadline_date)
            elif can_proceed and record.enquiry_type == 'fabrication_prototyping':
                can_proceed = bool(record.fabrication_capabilities)

            record.can_move_to_presales = can_proceed
        
    @api.depends('sow_attachment_ids', 'total_cost')
    def _compute_can_send_for_approval(self):
        """Compute if the enquiry can be sent for approval"""
        for record in self:
            # Check if SOW is attached and cost information is filled
            record.can_send_for_approval = bool(
                record.sow_attachment_ids and
                record.total_cost and
                record.total_cost > 0
            )

    @api.depends('stage_id')
    def _compute_is_in_presales_stage(self):
        """Compute if the enquiry is in Pre-Sales stage"""
        for record in self:
            record.is_in_presales_stage = record.stage_id and record.stage_id.name == 'Pre-Sales'

    def action_show_presales_required(self):
        """Show message about required fields for Pre-Sales"""
        self.ensure_one()
        
        # Determine what's missing
        missing = []
        if not self.company_poc_id:
            missing.append('T-Works Point of Contact')
        if self.enquiry_type != 'events_workshops' and not self.attachment_ids:
            missing.append('PRD')
            
        if self.enquiry_type == 'design_engineering':
            if not self.design_brief:
                missing.append('Design Brief')
            if not self.requested_deadline_date:
                missing.append('Requested Deadline Date')
        elif self.enquiry_type == 'fabrication_prototyping':
            if not self.fabrication_capabilities:
                missing.append('Required Capabilities')
                
        message = _('Please complete the following fields before proceeding: %s') % ', '.join(missing)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Required Fields'),
                'message': message,
                'type': 'warning',
                'sticky': False,
            }
        }
    
    def action_show_approval_required(self):
        """Show message about required fields for sending for approval"""
        self.ensure_one()
        
        missing = []
        if not self.sow_attachment_ids:
            missing.append('Scope of Work (SOW)')
        if not self.total_cost or self.total_cost <= 0:
            missing.append('Cost Information')
            
        message = _('Please complete the following before sending for approval: %s') % ', '.join(missing)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Required Fields'),
                'message': message,
                'type': 'warning',
                'sticky': False,
            }
        }

    # Navigation methods for stage progression
    def action_move_to_presales(self):
        """Move the enquiry from Awaiting Requirements to Pre-Sales stage"""
        self.ensure_one()

        # Common validation for all enquiry types
        if not self.company_poc_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Missing Information'),
                    'message': _('Please select a T-Works Point of Contact before proceeding.'),
                    'type': 'warning',
                    'sticky': False,
                }
            }
        if self.enquiry_type != 'events_workshops' and not self.attachment_ids:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Missing Information'),
                    'message': _('Please attach the PRD before proceeding.'),
                    'type': 'warning',
                    'sticky': False,
                }
            }

        # Specific validation for design_engineering
        if self.enquiry_type == 'design_engineering':
            if not self.design_brief:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Missing Information'),
                        'message': _('Please fill in the design brief before proceeding.'),
                        'type': 'warning',
                        'sticky': False,
                    }
                }
            
            if not self.requested_deadline_date:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Missing Information'),
                        'message': _('Please specify a requested deadline date before proceeding.'),
                        'type': 'warning',
                        'sticky': False,
                    }
                }
        
        # Specific validation for fabrication_prototyping
        if self.enquiry_type == 'fabrication_prototyping' and not self.fabrication_capabilities:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Missing Information'),
                    'message': _('Please select at least one fabrication capability before proceeding.'),
                    'type': 'warning',
                    'sticky': False,
                }
            }

        # Find Pre-Sales stage
        presales_stage = self.env['enquiry.stage'].search([('name', '=', 'Pre-Sales')], limit=1)
        if presales_stage:
            self.write({
                'stage_id': presales_stage.id
            })
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Stage Changed'),
                    'message': _('Enquiry has been moved to Pre-Sales stage.'),
                    'type': 'success',
                    'sticky': False,
                }
            }
        return True

    def action_move_to_quotation(self):
        """Move the enquiry from Pre-Sales to Quotation Sent stage"""
        self.ensure_one()

        # Check if cost information is filled
        if not self.total_cost or self.total_cost <= 0:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Missing Cost Information'),
                    'message': _('Please ensure cost information is completed before sending quotation.'),
                    'type': 'warning',
                    'sticky': False,
                }
            }

        # Find Quotation Sent stage
        quotation_sent_stage = self.env['enquiry.stage'].search([('name', '=', 'Quotation Sent')], limit=1)
        if quotation_sent_stage:
            self.write({
                'stage_id': quotation_sent_stage.id,
                'quotation_approved': True,
                'quotation_approved_by_id': self.env.user.employee_id.id,
                'quotation_approval_date': fields.Datetime.now(),
            })
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Quotation Sent'),
                    'message': _('Enquiry has been moved to Quotation Sent stage.'),
                    'type': 'success',
                    'sticky': False,
                }
            }
        return True
    
    def action_move_to_quotation_sent(self):
        """Move the enquiry to the Quotation Sent stage after approval"""
        self.ensure_one()
        
        # Find Quotation Sent stage
        quotation_sent_stage = self.env['enquiry.stage'].search([('name', '=', 'Quotation Sent')], limit=1)
        
        if quotation_sent_stage:
            self.write({
                'stage_id': quotation_sent_stage.id
            })
            
            # Add a message to the chatter
            self.message_post(
                body=_('Quotation has been sent to the client. Enquiry moved to Quotation Sent stage.'),
                message_type='notification'
            )
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Stage Changed'),
                    'message': _('Enquiry has been moved to Quotation Sent stage.'),
                    'type': 'success',
                    'sticky': False,
                }
            }
        
        return True

    def action_move_to_active(self):
        """Move the enquiry from Quotation Sent to Active stage"""
        self.ensure_one()

        # Check if payment status indicates payment is processed
        if self.payment_status not in ['paid', 'po_approved', 'bank_verified']:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Payment Required'),
                    'message': _('Please complete payment processing before moving to Active stage.'),
                    'type': 'warning',
                    'sticky': False,
                }
            }

        # Find Active stage
        active_stage = self.env['enquiry.stage'].search([('name', '=', 'Active')], limit=1)
        if active_stage:
            self.write({
                'stage_id': active_stage.id
            })
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Stage Changed'),
                    'message': _('Enquiry has been moved to Active stage.'),
                    'type': 'success',
                    'sticky': False,
                }
            }
        return True
    
    # Add method to edit cost estimates
    def action_edit_cost_estimates(self):
        """Open the cost estimates wizard"""
        self.ensure_one()
        return {
            'name': _('Edit Cost Estimates'),
            'type': 'ir.actions.act_window',
            'res_model': 'enquiry.cost.estimate.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'active_id': self.id, 'active_model': 'enquiry.enquiry'}
        }

    is_project_manager = fields.Boolean(
        'Is Project Manager User',
        compute='_compute_is_project_manager',
        help="Technical field to check if current user is a Project Manager",
        store=False
    )

    @api.depends_context('uid')
    def _compute_is_project_manager(self):
        """Check if current user is a Project Manager"""
        is_pm = self.env.user.has_group('enquiry.group_enquiry_project_manager')
        for record in self:
            record.is_project_manager = is_pm
    
    # API integration methods
    def action_generate_api_token(self):
        """Generate a new API token for SmartSuite integration"""
        self.ensure_one()
        
        # Check if API is enabled in system settings
        if not self.api_enabled:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('API Disabled'),
                    'message': _('API functionality is disabled in system settings. Please enable it in Enquiry configuration.'),
                    'type': 'warning',
                    'sticky': True
                }
            }
        
        # Generate a new token with type 'api_access' valid for a longer period (30 days)
        token_record = self.env['enquiry.access.token'].sudo().generate_token(
            self.id, expiry_days=30, token_type='api_access'
        )
        
        # Store token info in the enquiry
        self.write({
            'api_token': token_record.token,
            'api_token_expiry': token_record.expiry_date
        })
        
        # Get base URL
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        
        # Provide confirmation with URLs
        pdf_url = f"{base_url}/api/proforma/{token_record.token}"
        json_url = f"{base_url}/api/enquiry/{token_record.token}"
        
        # Return a notification with the token info
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('API Token Generated'),
                'message': _(
                    f'API Token has been generated. Token: {token_record.token}\n'
                    f'PDF URL: {pdf_url}\n'
                    f'JSON URL: {json_url}\n'
                    f'Expires: {token_record.expiry_date}'
                ),
                'sticky': True,
                'type': 'success'
            }
        }
    
    def action_copy_pdf_url(self):
        """Copy PDF URL to clipboard"""
        self.ensure_one()
        
        # Check if API is enabled in system settings
        if not self.api_enabled:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('API Disabled'),
                    'message': _('API functionality is disabled in system settings. Please enable it in Enquiry configuration.'),
                    'type': 'warning'
                }
            }
        
        if not self.api_token:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No API Token'),
                    'message': _('Please generate an API token first.'),
                    'type': 'warning'
                }
            }
                
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        pdf_url = f"{base_url}/api/proforma/{self.api_token}"

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('PDF URL'),
                'message': pdf_url,
                'sticky': True,
                'type': 'success',
            }
        }
        
    def action_copy_json_url(self):
        """Copy JSON URL to clipboard"""
        self.ensure_one()
        
        # Check if API is enabled in system settings
        if not self.api_enabled:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('API Disabled'),
                    'message': _('API functionality is disabled in system settings. Please enable it in Enquiry configuration.'),
                    'type': 'warning'
                }
            }
        
        if not self.api_token:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No API Token'),
                    'message': _('Please generate an API token first.'),
                    'type': 'warning'
                }
            }
                
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        json_url = f"{base_url}/api/enquiry/{self.api_token}"

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('JSON URL'),
                'message': json_url,
                'sticky': True,
                'type': 'success',
            }
        }
    
    def action_mark_completed(self):
        """Mark the enquiry as completed and move it to the 'Completed' stage"""
        self.ensure_one()
        
        # Check for required dispatch attachments
        missing_attachments = []
        
        if not self.gate_pass_attachment_ids:
            missing_attachments.append('Gate Pass')
        
        if not self.invoice_attachment_ids:
            missing_attachments.append('Invoice')
        
        if not self.delivery_challan_attachment_ids:
            missing_attachments.append('Delivery Challan')
        
        # Photos are optional but recommended
        if not self.dispatch_photos_attachment_ids:
            # Just add a warning but don't block completion
            self.message_post(
                body=_('Note: No dispatch photos were uploaded.'),
                message_type='notification'
            )
        
        # If there are missing required attachments, show error
        if missing_attachments:
            missing_str = ', '.join(missing_attachments)
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Missing Required Attachments'),
                    'message': _('Please upload the following required documents before marking as completed: %s') % missing_str,
                    'type': 'warning',
                    'sticky': False,
                }
            }
        
        # Find Completed stage
        completed_stage = self.env['enquiry.stage'].search([('name', '=', 'Completed')], limit=1)
        if not completed_stage:
            # If Completed stage doesn't exist yet, create it
            completed_stage = self.env['enquiry.stage'].create({
                'name': 'Completed',
                'sequence': 8,
                'fold': True,
                'requirements': 'Project has been completed successfully.',
                'is_closed': False
            })
        
        # Move to Completed stage
        if completed_stage:
            self.write({
                'stage_id': completed_stage.id
            })
            
            # Post a message in the chatter
            self.message_post(
                body=_('Enquiry has been marked as completed with all required dispatch documents.'),
                message_type='notification'
            )
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Enquiry Completed'),
                    'message': _('Enquiry has been moved to Completed stage.'),
                    'type': 'success',
                    'sticky': False,
                }
            }
        
        return True
            
    # Add method to get JSON data of the enquiry
    def action_get_json(self):
        """Get JSON data of the enquiry including API URLs"""
        self.ensure_one()
        
        # Check if API is enabled
        if not self.api_enabled:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('API Disabled'),
                    'message': _('API functionality is disabled in system settings. Please enable it in Enquiry configuration.'),
                    'type': 'warning'
                }
            }
            
        # First generate API token if not exists
        if not self.api_token:
            self.action_generate_api_token()

        # Prepare custom line items as a list of dictionaries
        custom_items = []
        for item in self.custom_line_item_ids:
            custom_items.append({
                'service_name': item.name,
                'service_cost': item.cost,
                'description': item.notes,
            })
            
        # Get base URL
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        
        # Prepare the enquiry data as a dictionary
        enquiry_data = {
            'enquiry_id': self.name,
            'customer_info': {
                'name': self.customer_name,
                'email': self.email,
                'phone': self.phone,
                'company': self.company_name,
            },
            'project_info': {
                'name': self.project_name,
                'type': dict(self._fields['enquiry_type'].selection).get(self.enquiry_type),
            },
            'costs': {
                'design_time': self.design_time,
                'design_quantity': self.design_quantity,
                'design_cost': self.design_cost,
                'design_total': self.design_total,
                'material_cost': self.material_cost,
                'prototyping_quantity': self.prototyping_quantity,
                'prototyping_cost': self.prototyping_cost,
                'prototyping_total': self.prototyping_total,
                'total_cost': self.total_cost,
                'custom_services': custom_items,
            },
            'stage': self.stage_id.name,
            'api_urls': {
                'pdf_url': f"{base_url}/api/proforma/{self.api_token}" if self.api_token else None,
                'json_url': f"{base_url}/api/enquiry/{self.api_token}" if self.api_token else None,
            }
        }

        # Convert to JSON string
        json_data = json.dumps(enquiry_data, indent=4)

        # Show a notification with the JSON data
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Enquiry JSON Data'),
                'message': json_data,
                'type': 'success',
                'sticky': True,
            }
        }

# Pushing Active Projects from Enquiry to Custom Project Module
    
    def _track_stage_change(self, vals):
        if 'stage_id' not in vals:
            return

        for record in self:
            old_stage_id = record.stage_id.id
            old_stage_name = record.stage_id.name

            if record.stage_time_start:
                time_spent = fields.Datetime.now() - record.stage_time_start
                time_spent_hours = time_spent.total_seconds() / 3600

                self.env['enquiry.stage.history'].create({
                    'enquiry_id': record.id,
                    'stage_id': old_stage_id,
                    'stage_name': old_stage_name,
                    'date_start': record.stage_time_start,
                    'date_end': fields.Datetime.now(),
                    'time_spent_hours': time_spent_hours,
                })
        vals['stage_time_start'] = fields.Datetime.now()       

    def _update_presales_timestamp(self, vals):
        presales_fields = [
            'design_cost', 'material_cost', 'prototyping_cost',
            'design_quantity', 'prototyping_quantity',
            'custom_line_item_ids',
            'customer_name', 'company_name'
        ]
    
        if any(field in vals for field in presales_fields):
            vals['last_presales_updated'] = fields.Datetime.now()
     
    
    def _update_client_record(self, vals):
        client_fields = [
            'customer_name', 'email', 'phone', 'company_name',
            'billing_street', 'billing_city', 'billing_zip',
            'shipping_street', 'shipping_city', 'shipping_zip',
            'completed_registration', 'nda_required', 'bd_poc_id'
        ]
        if any(field in vals for field in client_fields):
            for record in self:
                if record.customer_name and record.email and record.phone:
                    record._create_update_client_record()

    def _sync_project_stage_and_create(self, vals):
        if 'stage_id' not in vals:
            return

        # Avoid infinite loops when syncing from project
        if self.env.context.get('syncing_stages'):
            return

        for record in self:
            # Sync project stage with proper mapping and bidirectional logic
            project = self.env['project.project'].search([('enquiry_id', '=', record.id)], limit=1)
            if project:
                enquiry_stage_name = record.stage_id.name

                # Map enquiry stages to project stages
                stage_mapping = {
                    'Awaiting Requirements': 'Awaiting Requirement',
                    'Pre-Sales': 'Pre Sales',
                    'Quotation Approved': 'Quotation Approved',
                    'Quotation Sent': 'Quotation Sent',
                    'Active': 'Active',
                    'Dispatch': 'Dispatch',
                    'Completed': 'Completed',
                    'Inactive/Closed': 'Closed'
                }

                # Define stages where enquiry drives project (until Active)
                enquiry_driven_stages = [
                    'Awaiting Requirements', 'Pre-Sales', 'Quotation Approved',
                    'Quotation Sent', 'Active'
                ]

                # Special case: Always sync if enquiry is closed
                if enquiry_stage_name == 'Inactive/Closed':
                    project_stage_name = stage_mapping.get(enquiry_stage_name)
                    if project_stage_name:
                        project_stage = self.env['project.project.stage'].search([('name', '=', project_stage_name)], limit=1)
                        if project_stage:
                            # Update project stage and create history entry
                            project.write({'stage_id': project_stage.id})
                            self.env['project.stage.history'].create({
                                'project_id': project.id,
                                'stage_id': project_stage.id,
                                'entered_date': fields.Date.today()
                            })

                # Normal sync: Enquiry drives project until Active stage
                elif enquiry_stage_name in enquiry_driven_stages:
                    project_stage_name = stage_mapping.get(enquiry_stage_name)
                    if project_stage_name:
                        project_stage = self.env['project.project.stage'].search([('name', '=', project_stage_name)], limit=1)
                        if project_stage:
                            # Update project stage and create history entry
                            project.write({'stage_id': project_stage.id})
                            self.env['project.stage.history'].create({
                                'project_id': project.id,
                                'stage_id': project_stage.id,
                                'entered_date': fields.Date.today()
                            })

                # After Active stage: Project drives enquiry (handled in project model)

            # Create project when enquiry reaches "Awaiting Requirements"
            active_stage = self.env['enquiry.stage'].search([('name', '=', 'Awaiting Requirements')], limit=1)
            if record.stage_id.id == active_stage.id:
                record._create_or_update_project()


    def _create_or_update_project(self):
        # Handle both design_engineering and fabrication_prototyping (design and fabricating)
        if self.enquiry_type in ['design_engineering', 'fabrication_prototyping']:
            Project = self.env['project.project']
            project = Project.search([('project_custom_id', '=', self.name)], limit=1)

            # Get the "Awaiting Requirement" stage for new projects
            awaiting_stage = self.env['project.project.stage'].search([('name', '=', 'Awaiting Requirement')], limit=1)

            vals = {
                'name': self.project_name,
                'project_poc': self.company_poc_id.id if self.company_poc_id else False,
                'customer_name': self.customer_name,
                'company_name': self.company_name,
                'enquiry_id': self.id,
                'date_start': self.create_date,
                'date_due': self.requested_deadline_date,
                'budget': self.total_cost,
            }

            # Set stage to "Awaiting Requirement" for new projects
            if awaiting_stage and not project:
                vals['stage_id'] = awaiting_stage.id

            if project:
                project.write(vals)  # Update existing project
            else:
                new_project = Project.create(vals)  # Create new project
                # Create stage history entry
                self.env['project.stage.history'].create({
                    'project_id': new_project.id,
                    'stage_id': awaiting_stage.id if awaiting_stage else False,
                    'entered_date': fields.Date.today()
                })

        elif self.enquiry_type == 'events_workshops':
            Event = self.env['event.event']
            event = Event.search([('enquiry_id', '=', self.id)], limit=1)
            vals = {
                'name': self.project_name,
                'enquiry_id': self.id,
                'customer_name': self.customer_name,
                'contact_phone': self.phone,
                'contact_email': self.email,
                'company_name': self.company_name,
                'venue': self.event_venue or 'to_be_decided',  # Default to "To Be Decided"
            }
            if event:
               event.write(vals)  # Update existing event
            else:
                Event.create(vals)  # Create new event


    def sync_presales_from_project(self):
        for enquiry in self:
            project = self.env['project.project'].search([('enquiry_id', '=', enquiry.id)], limit=1)
            if not project:
                continue

            # Only sync if enquiry is newer
            if enquiry.last_presales_updated and (
                not project.last_presales_updated or enquiry.last_presales_updated > project.last_presales_updated
            ):
                _logger.info(f"[SYNC] Syncing from Enquiry {enquiry.id} to Project {project.id}")
                _logger.info(f"[SYNC] Enquiry Timestamp: {enquiry.last_presales_updated} | Project Timestamp: {project.last_presales_updated}")

            # Sync scalar fields
            project.with_context(syncing_presales=True).write({
                'design_cost': enquiry.design_cost,
                'design_time': enquiry.design_time,
                'material_cost': enquiry.material_cost,
                'customer_name': enquiry.customer_name,
                'company_name': enquiry.company_name,
                'prototyping_cost': enquiry.prototyping_cost,
                'design_quantity': enquiry.design_quantity,
                'prototyping_quantity': enquiry.prototyping_quantity,
                'last_presales_updated': enquiry.last_presales_updated,
                'pr_document' : [(6, 0, enquiry.attachment_ids.ids)]
            })

            # Sync custom line items
            project.custom_line_item_ids.unlink()
            for line in enquiry.custom_line_item_ids:
                self.env['project.custom.line.item'].create({
                    'project_id': project.id,
                    'name': line.name,
                    'quantity': line.quantity,
                    'cost': line.cost,
                    'hsn_code': line.hsn_code,
                    'notes': line.notes,
                })

            project._compute_total_cost()  # Optional recomputation
    
    def sync_presales_to_project(self):
        """Sync presales data from enquiry to linked project"""
        synced_count = 0
        for enquiry in self:
            project = self.env['project.project'].search([('enquiry_id', '=', enquiry.id)], limit=1)
            if not project:
                continue

            _logger.info(f"[SYNC] Syncing from Enquiry {enquiry.id} to Project {project.id}")
            _logger.info(f"[SYNC] Enquiry Timestamp: {enquiry.last_presales_updated} | Project Timestamp: {project.last_presales_updated}")

            # Update project costing fields
            project.with_context(syncing_presales=True).write({
                'design_cost': enquiry.design_cost,
                'design_time': enquiry.design_time,
                'material_cost': enquiry.material_cost,
                'customer_name': enquiry.customer_name,
                'company_name': enquiry.company_name,
                'prototyping_cost': enquiry.prototyping_cost,
                'design_quantity': enquiry.design_quantity,
                'prototyping_quantity': enquiry.prototyping_quantity,
                'project_deadline': enquiry.project_deadline,
                'last_presales_updated': fields.Datetime.now(),
                'pr_document': [(6, 0, enquiry.attachment_ids.ids)]
            })

            # Sync custom line items
            project.custom_line_item_ids.unlink()
            for line in enquiry.custom_line_item_ids:
                self.env['project.custom.line.item'].create({
                    'project_id': project.id,
                    'name': line.name,
                    'quantity': line.quantity,
                    'cost': line.cost,
                    'hsn_code': line.hsn_code,
                    'notes': line.notes,
                })

            project._compute_total_cost()
            synced_count += 1

        # Return user feedback
        if synced_count > 0:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Sync Successful',
                    'message': f'Presales data synced to {synced_count} project(s) successfully.',
                    'type': 'success',
                    'sticky': False,
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Sync Needed',
                    'message': 'No linked projects found to sync.',
                    'type': 'warning',
                    'sticky': False,
                }
            }
           
    def _track_stage_change(self, vals):
        if 'stage_id' not in vals:
            return

        for record in self:
            old_stage_id = record.stage_id.id
            old_stage_name = record.stage_id.name

            if record.stage_time_start:
                time_spent = fields.Datetime.now() - record.stage_time_start
                time_spent_hours = time_spent.total_seconds() / 3600

                self.env['enquiry.stage.history'].create({
                    'enquiry_id': record.id,
                    'stage_id': old_stage_id,
                    'stage_name': old_stage_name,
                    'date_start': record.stage_time_start,
                    'date_end': fields.Datetime.now(),
                    'time_spent_hours': time_spent_hours,
                })
        vals['stage_time_start'] = fields.Datetime.now()       

    def _update_presales_timestamp(self, vals):
        presales_fields = [
            'design_cost', 'material_cost', 'prototyping_cost',
            'design_quantity', 'prototyping_quantity',
            'custom_line_item_ids','customer_name','company_name',
        ]
    
        if any(field in vals for field in presales_fields):
            vals['last_presales_updated'] = fields.Datetime.now()
     
    
    def _update_client_record(self, vals):
        client_fields = [
            'customer_name', 'email', 'phone', 'company_name',
            'billing_street', 'billing_city', 'billing_zip',
            'shipping_street', 'shipping_city', 'shipping_zip',
            'completed_registration', 'nda_required', 'bd_poc_id'
        ]
        if any(field in vals for field in client_fields):
            for record in self:
                if record.customer_name and record.email and record.phone:
                    record._create_update_client_record()

    def _sync_project_stage_and_create(self, vals):
        if 'stage_id' not in vals:
            return

        # Avoid infinite loops when syncing from project
        if self.env.context.get('syncing_stages'):
            return

        for record in self:
            # Sync project stage with proper mapping and bidirectional logic
            project = self.env['project.project'].search([('enquiry_id', '=', record.id)], limit=1)
            if project:
                enquiry_stage_name = record.stage_id.name

                # Map enquiry stages to project stages
                stage_mapping = {
                    'Awaiting Requirements': 'Awaiting Requirement',
                    'Pre-Sales': 'Pre Sales',
                    'Quotation Approved': 'Quotation Approved',
                    'Quotation Sent': 'Quotation Sent',
                    'Active': 'Active',
                    'Dispatch': 'Dispatch',
                    'Completed': 'Completed',
                    'Inactive/Closed': 'Closed'
                }

                # Define stages where enquiry drives project (until Active)
                enquiry_driven_stages = [
                    'Awaiting Requirements', 'Pre-Sales', 'Quotation Approved',
                    'Quotation Sent', 'Active'
                ]

                # Special case: Always sync if enquiry is closed
                if enquiry_stage_name == 'Inactive/Closed':
                    project_stage_name = stage_mapping.get(enquiry_stage_name)
                    if project_stage_name:
                        project_stage = self.env['project.project.stage'].search([('name', '=', project_stage_name)], limit=1)
                        if project_stage:
                            # Update project stage and create history entry
                            project.write({'stage_id': project_stage.id})
                            self.env['project.stage.history'].create({
                                'project_id': project.id,
                                'stage_id': project_stage.id,
                                'entered_date': fields.Date.today()
                            })

                # Normal sync: Enquiry drives project until Active stage
                elif enquiry_stage_name in enquiry_driven_stages:
                    project_stage_name = stage_mapping.get(enquiry_stage_name)
                    if project_stage_name:
                        project_stage = self.env['project.project.stage'].search([('name', '=', project_stage_name)], limit=1)
                        if project_stage:
                            # Update project stage and create history entry
                            project.write({'stage_id': project_stage.id})
                            self.env['project.stage.history'].create({
                                'project_id': project.id,
                                'stage_id': project_stage.id,
                                'entered_date': fields.Date.today()
                            })

                # After Active stage: Project drives enquiry (handled in project model)

            # Create project when enquiry reaches "Awaiting Requirements"
            active_stage = self.env['enquiry.stage'].search([('name', '=', 'Awaiting Requirements')], limit=1)
            if record.stage_id.id == active_stage.id:
                record._create_or_update_project()


    def _create_or_update_event(self):
        """Create or update event from enquiry data"""
        if self.enquiry_type != 'events_workshops':
            return

        try:
            Event = self.env['event.event']
            event = Event.search([('enquiry_id', '=', self.id)], limit=1)

            # Get presales stage
            presales_stage = self.env.ref('tworks_events.event_stage_presales', raise_if_not_found=False)

            # Build vals dict with safe field access
            vals = {
                # Basic Info
                'name': getattr(self, 'event_name', None) or 'New Event',
                'enquiry_id': self.id,
            }

            # Only add stage if it exists
            if presales_stage:
                vals['stage_id'] = presales_stage.id

            # Contact Details - ensure all customer data is transferred
            if hasattr(self, 'customer_name') and self.customer_name:
                vals['customer_name'] = self.customer_name
            if hasattr(self, 'phone') and self.phone:
                vals['contact_phone'] = self.phone
            if hasattr(self, 'email') and self.email:
                vals['contact_email'] = self.email

            # Organization Details - ensure company name is transferred
            if hasattr(self, 'company_name') and self.company_name:
                vals['company_name'] = self.company_name
                # Use email as company email if available, otherwise use company name as fallback
                vals['company_email'] = self.email if self.email else self.company_name

            # Event Details - only add if fields exist and have values
            if hasattr(self, 'event_type') and self.event_type:
                vals['event_type'] = self.event_type
            if hasattr(self, 'event_theme') and self.event_theme:
                vals['event_theme'] = self.event_theme
            if hasattr(self, 'event_description') and self.event_description:
                vals['description'] = self.event_description
            if hasattr(self, 'estimated_attendees') and self.estimated_attendees:
                vals['estimated_attendees'] = self.estimated_attendees

            # Venue & Timing - handle venue requirement properly
            # The venue field is required in event.event model, so we need to provide a default
            if hasattr(self, 'event_venue') and self.event_venue:
                vals['venue'] = self.event_venue
            else:
                # Provide default venue if not specified (required field)
                vals['venue'] = 'to_be_decided'  # Default to "To Be Decided" for new events

            if hasattr(self, 'event_time_slot') and self.event_time_slot:
                vals['time_slot'] = self.event_time_slot
            if hasattr(self, 'event_date_begin') and self.event_date_begin:
                vals['date_begin'] = self.event_date_begin
            if hasattr(self, 'event_date_end') and self.event_date_end:
                vals['date_end'] = self.event_date_end

            # Create or update event
            if event:
                event.write(vals)
                _logger.info(f"Updated event {event.id} for enquiry {self.id}")
                # Update enquiry's event_id if not set
                if not self.event_id:
                    self.event_id = event.id
                return event
            else:
                new_event = Event.create(vals)
                _logger.info(f"Created event {new_event.id} for enquiry {self.id}")
                # Link the new event to the enquiry
                self.event_id = new_event.id
                return new_event

        except Exception as e:
            # Log error but don't break the enquiry workflow
            _logger.error(f"Failed to create/update event for enquiry {self.id}: {e}")
            return False


class EnquiryRequirement(models.Model):
    _name = 'enquiry.requirement'
    _description = 'Enquiry Additional Requirements'
    _order = 'category, sequence, name'

    name = fields.Char('Requirement Item', required=True)
    category = fields.Selection([
        ('furniture', 'Furniture & Seating'),
        ('av_equipment', 'Audio/Visual Equipment'),
        ('catering', 'Catering & Food Service'),
        ('decoration', 'Decoration & Ambiance'),
        ('technical', 'Technical Equipment'),
        ('logistics', 'Logistics & Support'),
        ('entertainment', 'Entertainment'),
        ('other', 'Other Services'),
    ], string='Category', required=True, default='furniture')

    sequence = fields.Integer('Sequence', default=10)
    unit = fields.Char('Unit of Measurement', default='pcs')
    description = fields.Text('Description')
    active = fields.Boolean('Active', default=True)

    @api.model
    def create_default_requirements(self):
        """Create default requirement items"""
        default_requirements = [
            # Furniture & Seating
            {'name': 'Chairs', 'category': 'furniture', 'unit': 'pcs', 'sequence': 1},
            {'name': 'Tables', 'category': 'furniture', 'unit': 'pcs', 'sequence': 2},
            {'name': 'Round Tables', 'category': 'furniture', 'unit': 'pcs', 'sequence': 3},
            {'name': 'Cocktail Tables', 'category': 'furniture', 'unit': 'pcs', 'sequence': 4},
            {'name': 'Sofas', 'category': 'furniture', 'unit': 'pcs', 'sequence': 5},
            {'name': 'Bar Stools', 'category': 'furniture', 'unit': 'pcs', 'sequence': 6},

            # Audio/Visual Equipment
            {'name': 'Microphones', 'category': 'av_equipment', 'unit': 'pcs', 'sequence': 1},
            {'name': 'Speakers', 'category': 'av_equipment', 'unit': 'pcs', 'sequence': 2},
            {'name': 'Projectors', 'category': 'av_equipment', 'unit': 'pcs', 'sequence': 3},
            {'name': 'Screens', 'category': 'av_equipment', 'unit': 'pcs', 'sequence': 4},
            {'name': 'LED Displays', 'category': 'av_equipment', 'unit': 'pcs', 'sequence': 5},
            {'name': 'Sound System', 'category': 'av_equipment', 'unit': 'set', 'sequence': 6},
            {'name': 'Lighting Equipment', 'category': 'av_equipment', 'unit': 'set', 'sequence': 7},

            # Catering & Food Service
            {'name': 'Catering Service', 'category': 'catering', 'unit': 'persons', 'sequence': 1},
            {'name': 'Coffee/Tea Service', 'category': 'catering', 'unit': 'persons', 'sequence': 2},
            {'name': 'Welcome Drinks', 'category': 'catering', 'unit': 'persons', 'sequence': 3},
            {'name': 'Lunch Boxes', 'category': 'catering', 'unit': 'pcs', 'sequence': 4},
            {'name': 'Buffet Setup', 'category': 'catering', 'unit': 'set', 'sequence': 5},
            {'name': 'Bar Service', 'category': 'catering', 'unit': 'hours', 'sequence': 6},

            # Decoration & Ambiance
            {'name': 'Flower Arrangements', 'category': 'decoration', 'unit': 'pcs', 'sequence': 1},
            {'name': 'Balloons', 'category': 'decoration', 'unit': 'pcs', 'sequence': 2},
            {'name': 'Banners', 'category': 'decoration', 'unit': 'pcs', 'sequence': 3},
            {'name': 'Stage Decoration', 'category': 'decoration', 'unit': 'set', 'sequence': 4},
            {'name': 'Backdrop', 'category': 'decoration', 'unit': 'pcs', 'sequence': 5},
            {'name': 'Centerpieces', 'category': 'decoration', 'unit': 'pcs', 'sequence': 6},

            # Technical Equipment
            {'name': 'Laptops', 'category': 'technical', 'unit': 'pcs', 'sequence': 1},
            {'name': 'Tablets', 'category': 'technical', 'unit': 'pcs', 'sequence': 2},
            {'name': 'WiFi Setup', 'category': 'technical', 'unit': 'set', 'sequence': 3},
            {'name': 'Power Extensions', 'category': 'technical', 'unit': 'pcs', 'sequence': 4},
            {'name': 'Charging Stations', 'category': 'technical', 'unit': 'pcs', 'sequence': 5},

            # Logistics & Support
            {'name': 'Security Personnel', 'category': 'logistics', 'unit': 'persons', 'sequence': 1},
            {'name': 'Event Coordinators', 'category': 'logistics', 'unit': 'persons', 'sequence': 2},
            {'name': 'Registration Desk', 'category': 'logistics', 'unit': 'set', 'sequence': 3},
            {'name': 'Parking Arrangements', 'category': 'logistics', 'unit': 'spaces', 'sequence': 4},
            {'name': 'Transportation', 'category': 'logistics', 'unit': 'vehicles', 'sequence': 5},

            # Entertainment
            {'name': 'DJ Services', 'category': 'entertainment', 'unit': 'hours', 'sequence': 1},
            {'name': 'Live Band', 'category': 'entertainment', 'unit': 'hours', 'sequence': 2},
            {'name': 'Photographer', 'category': 'entertainment', 'unit': 'hours', 'sequence': 3},
            {'name': 'Videographer', 'category': 'entertainment', 'unit': 'hours', 'sequence': 4},
            {'name': 'MC/Host', 'category': 'entertainment', 'unit': 'hours', 'sequence': 5},

            # Other Services
            {'name': 'Cleaning Service', 'category': 'other', 'unit': 'hours', 'sequence': 1},
            {'name': 'Gift Bags', 'category': 'other', 'unit': 'pcs', 'sequence': 2},
            {'name': 'Certificates', 'category': 'other', 'unit': 'pcs', 'sequence': 3},
            {'name': 'Name Tags', 'category': 'other', 'unit': 'pcs', 'sequence': 4},
        ]

        for req_data in default_requirements:
            existing = self.search([('name', '=', req_data['name']), ('category', '=', req_data['category'])])
            if not existing:
                self.create(req_data)


class EnquiryRequirementLine(models.Model):
    _name = 'enquiry.requirement.line'
    _description = 'Enquiry Requirement Line'
    _order = 'requirement_id'

    enquiry_id = fields.Many2one('enquiry.enquiry', string='Enquiry', required=True, ondelete='cascade')
    requirement_id = fields.Many2one('enquiry.requirement', string='Requirement', required=True)
    quantity = fields.Float('Quantity', default=1.0, required=True)
    unit = fields.Char(related='requirement_id.unit', string='Unit', readonly=True)
    category = fields.Selection(related='requirement_id.category', string='Category', readonly=True)
    notes = fields.Text('Notes')

    @api.onchange('requirement_id')
    def _onchange_requirement_id(self):
        if self.requirement_id:
            self.unit = self.requirement_id.unit




    def _create_or_update_project(self):
        # Handle both design_engineering and fabrication_prototyping (design and fabricating)
        if self.enquiry_type in ['design_engineering', 'fabrication_prototyping']:
            Project = self.env['project.project']
            project = Project.search([('project_custom_id', '=', self.name)], limit=1)

            # Get the "Awaiting Requirement" stage for new projects
            awaiting_stage = self.env['project.project.stage'].search([('name', '=', 'Awaiting Requirement')], limit=1)

            vals = {
                'name': self.project_name,
                'project_poc': self.company_poc_id.id if self.company_poc_id else False,
                'customer_name': self.customer_name,
                'company_name': self.company_name,
                'enquiry_id': self.id,
                'date_start': self.create_date,
                'date_due': self.project_deadline,
                'budget': self.total_cost,
            }

            # Set stage to "Awaiting Requirement" for new projects
            if awaiting_stage and not project:
                vals['stage_id'] = awaiting_stage.id

            if project:
                project.write(vals)  # Update existing project
            else:
                new_project = Project.create(vals)  # Create new project
                # Create stage history entry
                self.env['project.stage.history'].create({
                    'project_id': new_project.id,
                    'stage_id': awaiting_stage.id if awaiting_stage else False,
                    'entered_date': fields.Date.today()
                })

        elif self.enquiry_type == 'events_workshops':
            self._create_or_update_event()

    def action_sync_event(self):
        """Manual action to sync event from enquiry"""
        self.ensure_one()

        if self.enquiry_type != 'events_workshops':
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Not Applicable',
                    'message': 'Event sync is only available for Events/Workshops enquiries.',
                    'type': 'warning',
                    'sticky': False,
                }
            }

        try:
            event = self._create_or_update_event()
            if event:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Success!',
                        'message': f'Event "{event.name}" has been created/updated successfully.',
                        'type': 'success',
                        'sticky': False,
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Error',
                        'message': 'Failed to create/update event. Please check the logs for details.',
                        'type': 'danger',
                        'sticky': True,
                    }
                }
        except Exception as e:
            _logger.error(f"Manual event sync failed for enquiry {self.id}: {e}")
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Error',
                    'message': f'Event sync failed: {str(e)}',
                    'type': 'danger',
                    'sticky': True,
                }
            }

    def action_open_event(self):
        """Open the related event record"""
        self.ensure_one()

        if not self.event_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Event Found',
                    'message': 'No event is linked to this enquiry. Use "Sync Event" to create one.',
                    'type': 'warning',
                    'sticky': False,
                }
            }

        return {
            'type': 'ir.actions.act_window',
            'name': 'Related Event',
            'res_model': 'event.event',
            'res_id': self.event_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def sync_presales_from_project(self):
        for enquiry in self:
            project = self.env['project.project'].search([('enquiry_id', '=', enquiry.id)], limit=1)
            if not project:
                continue

            # Only sync if enquiry is newer
            if enquiry.last_presales_updated and (
                not project.last_presales_updated or enquiry.last_presales_updated > project.last_presales_updated
            ):
                _logger.info(f"[SYNC] Syncing from Enquiry {enquiry.id} to Project {project.id}")
                _logger.info(f"[SYNC] Enquiry Timestamp: {enquiry.last_presales_updated} | Project Timestamp: {project.last_presales_updated}")

            # Sync scalar fields
            project.with_context(syncing_presales=True).write({
                'design_cost': enquiry.design_cost,
                'design_time': enquiry.design_time,
                'material_cost': enquiry.material_cost,
                'customer_name': enquiry.customer_name,
                'company_name': enquiry.company_name,
                'prototyping_cost': enquiry.prototyping_cost,
                'design_quantity': enquiry.design_quantity,
                'prototyping_quantity': enquiry.prototyping_quantity,
                'last_presales_updated': enquiry.last_presales_updated,
                'pr_document' : [(6, 0, enquiry.attachment_ids.ids)]
            })

            # Sync custom line items
            project.custom_line_item_ids.unlink()
            for line in enquiry.custom_line_item_ids:
                self.env['project.custom.line.item'].create({
                    'project_id': project.id,
                    'name': line.name,
                    'quantity': line.quantity,
                    'cost': line.cost,
                    'hsn_code': line.hsn_code,
                    'notes': line.notes,
                })

            project._compute_total_cost()  # Optional recomputation
    

class EnquiryFabricationCapability(models.Model):
    _name = 'enquiry.fabrication.capability'
    _description = 'Fabrication Capability'
    _order = 'sequence, name'

    name = fields.Char('Capability Name', required=True)
    sequence = fields.Integer('Sequence', default=10)
    description = fields.Text('Description')

    @api.model
    def _get_default_capabilities(self):
        """Create default fabrication capabilities"""
        capabilities = [
            ('metal_shop', 'Metal Shop'),
            ('3d_printing', '3D Printing'),
            ('laser_cutting', 'Laser Cutting'),
            ('wood_shop', 'Wood Shop'),
            ('ceramic_studio', 'Ceramic Studio'),
            ('advanced_rapid_prototyping', 'Advanced Rapid Prototyping (ARP)'),
            ('electronics', 'Electronics'),
            ('textile', 'Textile')
        ]

        for key, name in capabilities:
            existing = self.search([('name', '=', name)], limit=1)
            if not existing:
                self.create({'name': name})

class EnquiryCustomLineItem(models.Model):
    _name = 'enquiry.custom.line.item'
    _description = 'Custom Line Item'

    sequence = fields.Integer(default=10)
    name = fields.Char('Item Name', required=True)
    quantity = fields.Integer('Quantity', default=1, required=True)
    cost = fields.Float('Cost', required=True)
    notes = fields.Text('Notes')
    hsn_code = fields.Char('HSN Code', default='998130', readonly=True)
    enquiry_id = fields.Many2one('enquiry.enquiry', required=True, string='Enquiry', ondelete='cascade')